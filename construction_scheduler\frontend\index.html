<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>施工进度编制系统</title>
    <!-- 引入Chart.js和甘特图插件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-gantt"></script>
    <style>
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
    text-align: center;
}
main {
    flex: 1;
    display: flex;
    overflow: hidden;
}
.sidebar {
    width: 300px;
    border-right: 1px solid #ccc;
    overflow-y: auto;
    padding: 1rem;
}
.content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}
#ganttChart {
    width: 100%;
    height: 100%;
}

/* 调试界面样式 */
.debug-tabs {
    margin-bottom: 1rem;
}
.tab-btn {
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    background: #f0f0f0;
    border: none;
    cursor: pointer;
}
.tab-btn.active {
    background: #2c3e50;
    color: white;
}
.tab-content {
    display: none;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}
.tab-content.active {
    display: block;
}
#httpMethod {
    width: 20%;
    padding: 0.5rem;
    margin-right: 1rem;
}
#apiPath {
    width: 70%;
    padding: 0.5rem;
    margin: 0.5rem 0 1rem 0;
}
textarea {
    width: 100%;
    height: 100px;
    margin-bottom: 1rem;
}
.response-section {
    margin-top: 1rem;
    background: #fff;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}
#responseOutput {
    margin: 0;
    font-family: monospace;
}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>施工进度编制系统</h1>
        </header>
        <main>
            <div class="sidebar">
                <h2>调试工具</h2>
                <div class="debug-tabs">
                    <button class="tab-btn active" onclick="openTab(event, 'apiDebug')">API测试</button>
                    <button class="tab-btn" onclick="openTab(event, 'dbStatus')">数据库状态</button>
                </div>

                <div id="apiDebug" class="tab-content active">
                    <h3>API请求测试</h3>
                    <select id="httpMethod">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                    <input type="text" id="apiPath" value="/tasks" placeholder="API路径">
                    <textarea id="requestBody" placeholder="请求体（JSON格式）"></textarea>
                    <button onclick="sendApiRequest()">发送请求</button>
                    <div class="response-section">
                        <h4>响应结果：</h4>
                        <pre id="responseOutput">等待响应...</pre>
                    </div>
                </div>

                <div id="dbStatus" class="tab-content">
                    <h3>数据库状态</h3>
                    <div id="dbInfo">
                        <p>任务总数：<span id="taskCount">0</span></p>
                        <p>最后更新：<span id="lastUpdate">从未</span></p>
                        <button onclick="refreshDbStatus()">刷新状态</button>
                    </div>
                </div>
            </div>
            <div class="sidebar">
                <h2>任务管理</h2>
                <table class="task-table" id="taskTable">
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>工期(天)</th>
                            <th>开始日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 任务数据将动态插入 -->
                    </tbody>
                </table>
            </div>
            <div class="content">
                <h2>进度视图</h2>
                <canvas id="ganttChart"></canvas>
            </div>
        </main>
    </div>
    <!-- 在body底部添加编辑模态框 -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>编辑任务</h3>
            <form id="editForm">
                <input type="hidden" id="taskId">
                <label>任务名称:
                    <input type="text" id="taskName" required>
                </label>
                <label>工期(天):
                    <input type="number" id="taskDuration" min="1" required>
                </label>
                <label>开始日期:
                    <input type="date" id="taskStartDate">
                </label>
                <button type="submit">保存更改</button>
            </form>
        </div>
    </div>

    <style>
    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }
    .modal-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 300px;
        border-radius: 4px;
    }
    .close {
        float: right;
        cursor: pointer;
        font-size: 24px;
    }
    form label {
        display: block;
        margin: 1rem 0;
    }
    </style>
    <script>
        // 渲染任务表格
        function renderTaskTable(tasks) {
            const taskBody = document.querySelector('#taskTable tbody');
            taskBody.innerHTML = '';
            
            tasks.forEach(task => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${task.name}</td>
                    <td>${task.duration}</td>
                    <td>${task.start_date || '-'}</td>
                    <td>
                        <button class="edit-btn" data-id="${task.id}" data-name="${task.name}" data-duration="${task.duration}" data-start-date="${task.start_date || ''}">编辑</button>
                        <button class="delete-btn" data-id="${task.id}">删除</button>
                    </td>
                `;
                taskBody.appendChild(tr);
            });
            
            // 添加按钮事件监听器
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.getElementById('taskId').value = btn.dataset.id;
                    document.getElementById('taskName').value = btn.dataset.name;
                    document.getElementById('taskDuration').value = btn.dataset.duration;
                    document.getElementById('taskStartDate').value = btn.dataset.startDate;
                    document.getElementById('editModal').style.display = 'block';
                });
            });
            
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    if (confirm('确定要删除此任务吗？')) {
                        fetch(`http://localhost:5000/tasks/${btn.dataset.id}`, {
                            method: 'DELETE'
                        }).then(() => location.reload());
                    }
                });
            });
        }

        // 修改页面加载时的调用
        window.onload = function() {
            fetch('http://localhost:5000/tasks')
                .then(response => response.json())
                .then(data => {
                    renderTaskTable(data);
                    renderGanttChart(data);
                });
        };

        // 处理编辑表单提交
        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const taskId = document.getElementById('taskId').value;
            const updatedTask = {
                name: document.getElementById('taskName').value,
                duration: parseInt(document.getElementById('taskDuration').value),
                start_date: document.getElementById('taskStartDate').value || null
            };
            
            fetch(`http://localhost:5000/tasks/${taskId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updatedTask)
            }).then(() => {
                document.getElementById('editModal').style.display = 'none';
                location.reload();
            });
        });

        // 关闭模态框功能
        document.querySelector('.close').addEventListener('click', () => {
            document.getElementById('editModal').style.display = 'none';
        });


        // 渲染甘特图
        function renderGanttChart(tasks) {
            const ctx = document.getElementById('ganttChart').getContext('2d');
            
            // 简单的甘特图配置（后续将扩展为完整的关键路径计算）
            const chartData = {
                labels: tasks.map(task => task.name),
                datasets: [{
                    label: '工期',
                    data: tasks.map(task => ({
                        x: task.duration,
                        y: task.name
                    })),
                    borderColor: '#4CAF50',
                    backgroundColor: '#4CAF50',
                    borderWidth: 1
                }]
            };

            new Chart(ctx, {
                type: 'ganttChart',
                data: chartData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: '工期（天）' }
                        },
                        y: {
                            type: 'category',
                            position: 'left',
                            offset: true
                        }
                    }
                }
            });
        }
        // 调试界面功能 - 选项卡切换
        function openTab(evt, tabName) {
            const tabs = document.getElementsByClassName('tab-content');
            const tabButtons = document.getElementsByClassName('tab-btn');
            
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
                tabButtons[i].classList.remove('active');
            }
            
            document.getElementById(tabName).classList.add('active');
            evt.currentTarget.classList.add('active');
        }

        // 调试界面功能 - API测试
        async function sendApiRequest() {
            const method = document.getElementById('httpMethod').value;
            const path = document.getElementById('apiPath').value;
            const body = document.getElementById('requestBody').value;
            const responseOutput = document.getElementById('responseOutput');
            
            try {
                const options = {
                    method: method,
                    headers: { 'Content-Type': 'application/json' }
                };
                
                if (method !== 'GET' && method !== 'DELETE') {
                    options.body = body;
                }
                
                const response = await fetch(`http://localhost:5000${path}`, options);
                const text = await response.text();
                
                responseOutput.textContent = `状态码: ${response.status} ${response.statusText}\n\n响应内容:\n${text}`;
            } catch (error) {
                responseOutput.textContent = `错误: ${error.message}`;
            }
        }

        // 调试界面功能 - 数据库状态刷新
        async function refreshDbStatus() {
            try {
                const response = await fetch('http://localhost:5000/debug/db-status');
                const data = await response.json();
                
                document.getElementById('taskCount').textContent = data.taskCount;
                document.getElementById('lastUpdate').textContent = data.lastUpdate;
            } catch (error) {
                console.error('获取数据库状态失败:', error);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            fetch('http://localhost:5000/tasks')
                .then(response => response.json())
                .then(data => {
                    renderTaskTable(data);
                    renderGanttChart(data);
                });
            
            refreshDbStatus();
        };
        
    </script>
</body>
</html>