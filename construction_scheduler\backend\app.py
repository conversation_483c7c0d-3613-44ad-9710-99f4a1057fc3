// 初始化Flask应用
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS  # 导入CORS模块
from datetime import datetime

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///scheduler.db'
CORS(app)  # 启用CORS

db = SQLAlchemy(app)

// 定义任务模型
class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    duration = db.Column(db.Integer, nullable=False)  # 工期（天）
    start_date = db.Column(db.Date)  # 实际开始日期
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)  # 更新时间
    
    def __repr__(self):
        return f'<Task {self.id}>'

// 创建数据库表
@app.before_first_request
def create_tables():
    db.create_all()

// 任务管理接口
@app.route('/tasks', methods=['GET'])
def get_tasks():
    tasks = Task.query.all()
    return jsonify([{
        'id': task.id,
        'name': task.name,
        'duration': task.duration,
        'start_date': task.start_date.strftime('%Y-%m-%d') if task.start_date else None
    } for task in tasks])

@app.route('/tasks', methods=['POST'])
def create_task():
    data = request.get_json()
    new_task = Task(
        name=data['name'],
        duration=data['duration']
    )
    db.session.add(new_task)
    db.session.commit()
    return jsonify({'message': 'Task created'}), 201

// 获取单个任务
@app.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    task = Task.query.get_or_404(task_id)
    return jsonify({
        'id': task.id,
        'name': task.name,
        'duration': task.duration,
        'start_date': task.start_date.strftime('%Y-%m-%d') if task.start_date else None
    })

// 更新任务
@app.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    task = Task.query.get_or_404(task_id)
    data = request.get_json()
    
    task.name = data['name']
    task.duration = data['duration']
    if 'start_date' in data:
        task.start_date = datetime.strptime(data['start_date'], '%Y-%m-%d') if data['start_date'] else None
    
    db.session.commit()
    return jsonify({'message': '任务更新成功'})

// 删除任务
@app.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)
    db.session.delete(task)
    db.session.commit()
    return jsonify({'message': '任务删除成功'})

// 获取数据库状态
@app.route('/debug/db-status', methods=['GET'])
def get_db_status():
    task_count = Task.query.count()
    last_task = Task.query.order_by(Task.id.desc()).first()
    last_update = last_task.updated_at if hasattr(last_task, 'updated_at') else datetime.now()
    
    return jsonify({
        'taskCount': task_count,
        'lastUpdate': last_update.strftime('%Y-%m-%d %H:%M:%S')
    })

// 健康检查接口
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'database': 'connected',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })
